'use client'

import React, { useState, useEffect } from 'react'
import { useTranslation } from 'react-i18next'
import { RiCloseLine } from '@remixicon/react'
import Modal from '@/app/components/base/modal'
import Button from '@/app/components/base/button'
import { fetchEnterpriseKnowledgeBases } from '@/service/datasets'
import type { ExternalKnowledgeItem } from '@/models/datasets'

type KnowledgeBaseSelectorModalProps = {
  onClose: () => void
  onSelect: (knowledgeId: string) => void
}

const KnowledgeBaseSelectorModal: React.FC<KnowledgeBaseSelectorModalProps> = ({ onClose, onSelect }) => {
  const { t } = useTranslation()
  const [activeTab, setActiveTab] = useState(0)
  const [knowledgeBases, setKnowledgeBases] = useState<ExternalKnowledgeItem[]>([])
  const [loading, setLoading] = useState(false)
  const [selectedKnowledgeId, setSelectedKnowledgeId] = useState<string | null>(null)

  const tabs = [
    { name: t('dataset.externalKnowledge.tabs.public'), type: 0 },
    { name: t('dataset.externalKnowledge.tabs.organization'), type: 1 },
    { name: t('dataset.externalKnowledge.tabs.personal'), type: 2 },
  ]

  useEffect(() => {
    const fetchKnowledgeBases = async () => {
      setLoading(true)
      try {
        const result = await fetchEnterpriseKnowledgeBases({ type: activeTab, size: 100 })
        setKnowledgeBases(result.data.list)
      }
      catch (error) {
        console.error('Error fetching knowledge bases:', error)
      }
      finally {
        setLoading(false)
      }
    }
    fetchKnowledgeBases()
  }, [activeTab])

  const handleSelect = () => {
    if (selectedKnowledgeId)
      onSelect(selectedKnowledgeId)
  }

  return (
    <Modal isShow={true} onClose={onClose} className="!w-[640px]">
      <div className="flex flex-col">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold text-gray-900">{t('dataset.externalKnowledge.selectorModalTitle')}</h2>
          <Button onClick={onClose} className="p-1">
            <RiCloseLine className="h-5 w-5 text-gray-500" />
          </Button>
        </div>
        <div className="flex border-b border-gray-200">
          {tabs.map((tab, index) => (
            <button
              key={tab.type}
              onClick={() => setActiveTab(index)}
              className={`px-4 py-2 text-sm font-medium ${activeTab === index ? 'border-b-2 border-primary-600 text-primary-600' : 'text-gray-500 hover:text-gray-700'}`}
            >
              {tab.name}
            </button>
          ))}
        </div>
        <div className="mt-4 h-[300px] overflow-y-auto">
          {loading
            ? (
              <div className="flex justify-center items-center h-full">
                <p>{t('dataset.externalKnowledge.loading')}</p>
              </div>
            )
            : (
              <ul className="space-y-2">
                {knowledgeBases.map(kb => (
                  <li
                    key={kb.id}
                    onClick={() => setSelectedKnowledgeId(kb.id)}
                    className={`p-2 rounded-md cursor-pointer ${selectedKnowledgeId === kb.id ? 'bg-primary-50 border border-primary-200' : 'hover:bg-gray-50'}`}
                  >
                    <p className="font-semibold text-gray-800">{kb.name}</p>
                    <p className="text-sm text-gray-500">{kb.id}</p>
                  </li>
                ))}
              </ul>
            )}
        </div>
        <div className="flex justify-end mt-6">
          <Button onClick={onClose} className="mr-2">{t('common.operation.cancel')}</Button>
          <Button variant="primary" onClick={handleSelect} disabled={!selectedKnowledgeId}>{t('common.operation.ok')}</Button>
        </div>
      </div>
    </Modal>
  )
}

export default KnowledgeBaseSelectorModal
